# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error,reportReturnType=error
"""
Business logic handler for Contact Account Change Events.
Handles contact re-assignment operations.
Contact (Salesforce) = Legal Personnel (PostgreSQL)
"""
import typing

# Lazy imports to avoid Django settings issues
logger = None


class ContactChangeHandler:
    """
    Handles the business logic for Contact Account Change Events.
    Separated from the listener for better testability and maintainability.
    Contact (Salesforce) = Legal Personnel (PostgreSQL)
    """

    def __init__(self):
        self._ensure_setup()

    def _ensure_setup(self) -> None:
        """
        Ensure dependencies are loaded (lazy loading)
        """
        global logger

        if logger is None:
            try:
                from ...shared.utils.logger import get_streaming_logger

                logger = get_streaming_logger()
            except Exception:
                # Fallback to basic logging if streaming logger fails
                import logging

                logger = logging.getLogger(__name__)

    def _get_logger(self):
        """Get logger instance, ensuring it's initialized"""
        global logger
        if logger is None:
            self._ensure_setup()
        return logger

    async def process_contact_reassignment(
        self,
        contact_gain_id: str,
        new_account_gain_id: str,
        account_name: typing.Optional[str] = None,
    ) -> None:
        """
        Process the contact re-assignment by updating the legalpersonnel record
        Contact (Salesforce) = Legal Personnel (PostgreSQL)
        """
        try:
            self._get_logger().info(
                f"Processing contact reassignment: {contact_gain_id} -> {new_account_gain_id}",
                extra={
                    'route_section': 'Contact_Reassignment',
                    'additional_details': f"Account: {account_name}",
                },
            )

            # Update the database
            updated = await self._update_legalpersonnel_account(
                contact_gain_id, new_account_gain_id, account_name
            )

            if updated:

                self._get_logger().info(
                    f"Successfully processed contact reassignment for {contact_gain_id}",
                    extra={
                        'route_section': 'Contact_Reassignment',
                        'additional_details': f"New Account: {new_account_gain_id}",
                    },
                )
            else:
                self._get_logger().warning(
                    f"No legalpersonnel record found for contact {contact_gain_id}",
                    extra={
                        'route_section': 'Contact_Reassignment',
                        'additional_details': f"Contact ID: {contact_gain_id}",
                    },
                )

        except Exception as e:
            self._get_logger().error(
                f"Error processing contact reassignment: {str(e)}",
                extra={
                    'route_section': 'Contact_Reassignment',
                    'additional_details': f"Contact: {contact_gain_id}, Account: {new_account_gain_id}",
                },
            )

    async def _update_legalpersonnel_account(
        self,
        contact_gain_id: str,
        new_account_gain_id: str,
        account_name: str | None = None,
    ) -> bool:
        """
        Update the legalpersonnel record with the new account
        """
        try:
            # Lazy import to avoid Django settings issues
            from datetime import datetime

            import psycopg

            from integration.integration_code import aws_operations

            # Use the existing database operations
            conn_string = aws_operations.get_postgres_conn_string()
            connection = psycopg.connect(conn_string)
            cursor = connection.cursor()

            # Update the legalpersonnel record
            if account_name:
                update_query = """
                    UPDATE legalpersonnel
                    SET lawfirmid = %s,
                        lawfirmname = %s,
                        modifieddatetime = %s
                    WHERE gainid = %s
                """
                cursor.execute(
                    update_query,
                    (
                        new_account_gain_id,
                        account_name,
                        datetime.now(),
                        contact_gain_id,
                    ),
                )
            else:
                update_query = """
                    UPDATE legalpersonnel
                    SET lawfirmid = %s,
                        modifieddatetime = %s
                    WHERE gainid = %s
                """
                cursor.execute(
                    update_query,
                    (new_account_gain_id, datetime.now(), contact_gain_id),
                )

            rows_affected = cursor.rowcount
            connection.commit()

            self._get_logger().info(
                f"Updated {rows_affected} legalpersonnel record(s) for contact {contact_gain_id}",
                extra={
                    'route_section': 'Database_Update',
                    'additional_details': f"Rows affected: {rows_affected}",
                },
            )

            return rows_affected > 0

        except Exception as e:
            self._get_logger().error(
                f"Database error updating legalpersonnel: {str(e)}",
                extra={
                    'route_section': 'Database_Update',
                    'additional_details': f"Contact: {contact_gain_id}, Account: {new_account_gain_id}",
                },
            )
            return False
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'connection' in locals():
                connection.close()

    async def _validate_contact_exists(self, contact_gain_id: str) -> bool:
        """
        Validate that the contact exists in the legalpersonnel table
        """
        try:
            # Lazy import to avoid Django settings issues
            import psycopg

            from integration.integration_code import aws_operations

            conn_string = aws_operations.get_postgres_conn_string()
            connection = psycopg.connect(conn_string)
            cursor = connection.cursor()

            query = "SELECT COUNT(*) FROM legalpersonnel WHERE gainid = %s"
            cursor.execute(query, (contact_gain_id,))

            result = cursor.fetchone()
            exists = bool(result and result[0] > 0)

            self._get_logger().debug(
                f"Contact {contact_gain_id} exists: {exists}",
                extra={
                    'route_section': 'Contact_Validation',
                    'additional_details': f"Contact ID: {contact_gain_id}",
                },
            )

            return exists

        except Exception as e:
            self._get_logger().error(
                f"Error validating contact existence: {str(e)}",
                extra={
                    'route_section': 'Contact_Validation',
                    'additional_details': f"Contact: {contact_gain_id}",
                },
            )
            return False
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'connection' in locals():
                connection.close()
