# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error,reportReturnType=error
"""
Abstract base class for Salesforce Platform Event listeners.
Provides a framework for easily adding new event listeners.
"""
import abc
import logging
import typing

if typing.TYPE_CHECKING:
    from ..client.cometd_client import SalesforceStreamingClient

logger: typing.Optional[logging.Logger] = None


class BaseSalesforceEventListener(abc.ABC):
    """
    Abstract base class for Salesforce Platform Event listeners.
    Provides a framework for easily adding new event listeners.
    """

    def __init__(self, channel: str, test: bool = False):
        self.channel = channel
        self.test = test
        self.streaming_client: typing.Optional['SalesforceStreamingClient'] = (
            None
        )
        self._ensure_setup()

    def _ensure_setup(self) -> None:
        """
        Ensure dependencies are loaded (lazy loading)
        """
        global logger

        if logger is None:
            try:
                from ..utils.logger import get_streaming_logger

                logger = get_streaming_logger()
            except Exception:
                # Fallback to basic logging if streaming logger fails
                logger = logging.getLogger(__name__)

    def _get_logger(self) -> logging.Logger:
        """Get the logger, ensuring it's initialized"""
        global logger
        if logger is None:
            self._ensure_setup()
        assert (
            logger is not None
        ), "Logger should be initialized after _ensure_setup"
        return logger

    @abc.abstractmethod
    async def handle_event(
        self, message: typing.Dict[str, typing.Any]
    ) -> None:
        """
        Handle incoming event. Must be implemented by subclasses.
        """
        pass

    async def start_listening(self) -> None:
        """
        Start listening for events on the configured channel.
        """
        try:
            # Lazy import of SalesforceStreamingClient
            from ..client.cometd_client import SalesforceStreamingClient

            self.streaming_client = SalesforceStreamingClient(test=self.test)

            if not await self.streaming_client.authenticate():
                self._get_logger().error(
                    f"Failed to authenticate for {self.channel}",
                    extra={
                        'route_section': f'{self.__class__.__name__}_Startup',
                        'additional_details': f"Channel: {self.channel}",
                    },
                )
                return

            if not await self.streaming_client.connect():
                self._get_logger().error(
                    f"Failed to connect for {self.channel}",
                    extra={
                        'route_section': f'{self.__class__.__name__}_Startup',
                        'additional_details': f"Channel: {self.channel}",
                    },
                )
                return

            if not await self.streaming_client.subscribe(self.channel):
                self._get_logger().error(
                    f"Failed to subscribe to {self.channel}",
                    extra={
                        'route_section': f'{self.__class__.__name__}_Startup',
                        'additional_details': f"Channel: {self.channel}",
                    },
                )
                return

            self._get_logger().info(
                f"Successfully started listening for events on {self.channel}",
                extra={
                    'route_section': f'{self.__class__.__name__}_Startup',
                    'additional_details': f"Channel: {self.channel}",
                },
            )
            await self.streaming_client.listen(self.handle_event)

        except Exception as e:
            self._get_logger().error(
                f"Error starting listener for {self.channel}: {str(e)}",
                extra={
                    'route_section': f'{self.__class__.__name__}_Startup',
                    'additional_details': f"Channel: {self.channel}, Error: {str(e)}",
                },
            )

    async def stop_listening(self) -> None:
        """
        Stop listening and disconnect from the streaming client.
        """
        if self.streaming_client:
            try:
                await self.streaming_client.disconnect()
                self._get_logger().info(
                    f"Stopped listening for events on {self.channel}",
                    extra={
                        'route_section': f'{self.__class__.__name__}_Shutdown',
                        'additional_details': f"Channel: {self.channel}",
                    },
                )
            except Exception as e:
                self._get_logger().error(
                    f"Error stopping listener for {self.channel}: {str(e)}",
                    extra={
                        'route_section': f'{self.__class__.__name__}_Shutdown',
                        'additional_details': f"Channel: {self.channel}, Error: {str(e)}",
                    },
                )
            finally:
                self.streaming_client = None
