# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error,reportReturnType=error
"""
CometD client for Salesforce Streaming API.
Handles low-level CometD protocol communication.
"""
import asyncio
import typing

import aiohttp

from ..utils.config import STREAMING_CONFIG

# Lazy imports to avoid Django settings issues during import
logger = None
app_config = None
credentials = None


class SalesforceStreamingClient:
    """
    CometD client for Salesforce Streaming API to listen to platform events
    """

    def __init__(self, test: bool = False):
        self._ensure_django_setup()
        self.test = test
        self.session: typing.Optional[aiohttp.ClientSession] = None
        self.sf_instance_url: typing.Optional[str] = None
        self.access_token: typing.Optional[str] = None
        self.client_id: typing.Optional[str] = None
        self.is_authenticated: bool = False
        self.is_connected: bool = False

        # Configuration from utils
        self.cometd_version = STREAMING_CONFIG['cometd_version']
        self.connection_timeout = STREAMING_CONFIG['connection_timeout']
        self.max_reconnect_attempts = STREAMING_CONFIG[
            'max_reconnect_attempts'
        ]
        self.reconnect_delay = STREAMING_CONFIG['reconnect_delay']

        # Track subscribed channels for reconnection
        self.subscribed_channels: typing.List[str] = []
        self.reconnect_attempts: int = 0

    def _ensure_django_setup(self) -> None:
        """
        Ensure Django dependencies are loaded (lazy loading)
        """
        global logger, app_config, credentials

        if logger is None:
            try:
                from ..utils.logger import get_streaming_logger

                logger = get_streaming_logger()
            except Exception:
                # Fallback to basic logging if streaming logger fails
                import logging

                logger = logging.getLogger(__name__)

        if app_config is None:
            try:
                from django.conf import settings

                app_config = settings
            except Exception:
                app_config = None

        if credentials is None:
            try:
                from integration.integration_code import salesforce_operations

                credentials = salesforce_operations
            except Exception:
                credentials = None

    async def authenticate(self) -> bool:
        """
        Authenticate with Salesforce and get access token
        """
        try:
            # Lazy import of salesforce_operations
            from integration.integration_code import salesforce_operations

            # Use the existing authenticate_salesforce function
            auth_success = salesforce_operations.authenticate_salesforce(
                self.test
            )

            if not auth_success:
                logger.error("Failed to authenticate with Salesforce")
                return False

            # Get the authenticated sf object and extract needed properties
            sf = salesforce_operations.sf
            if not sf:
                logger.error("Salesforce object is None after authentication")
                return False

            # Extract instance URL and access token from simple-salesforce object
            self.sf_instance_url = f"https://{sf.sf_instance}"
            self.access_token = sf.session_id
            self.is_authenticated = True

            logger.info(
                f"Successfully authenticated with Salesforce. Instance: {self.sf_instance_url}",
                extra={
                    'route_section': 'Salesforce_Authentication',
                    'additional_details': f"Test mode: {self.test}",
                },
            )
            return True

        except Exception as e:
            logger.error(
                f"Error during Salesforce authentication: {str(e)}",
                extra={
                    'route_section': 'Salesforce_Authentication',
                    'additional_details': f"Test mode: {self.test}",
                },
            )
            return False

    async def _create_session(self) -> None:
        """Create aiohttp session if not exists"""
        if not self.session:
            self.session = aiohttp.ClientSession()

    async def connect(self) -> bool:
        """
        Connect to Salesforce CometD endpoint
        """
        if not self.is_authenticated:
            logger.error("Must authenticate before connecting")
            return False

        try:
            await self._create_session()

            # Perform handshake
            if not await self._handshake():
                logger.error("Handshake failed")
                return False

            # Establish connection
            if not await self._connect():
                logger.error("Connection failed")
                return False

            self.is_connected = True
            logger.info(
                "Successfully connected to CometD",
                extra={
                    'route_section': 'CometD_Connection',
                    'additional_details': f"Client ID: {self.client_id}",
                },
            )
            return True

        except Exception as e:
            logger.error(
                f"Error during connection: {str(e)}",
                extra={
                    'route_section': 'CometD_Connection',
                    'additional_details': f"Client ID: {self.client_id}",
                },
            )
            return False

        return False

    async def _handshake(self) -> bool:
        """
        Perform CometD handshake
        """
        handshake_url = f"{self.sf_instance_url}/cometd/{self.cometd_version}/"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        handshake_message = [
            {
                "version": "1.0",
                "minimumVersion": "1.0",
                "channel": "/meta/handshake",
                "supportedConnectionTypes": ["long-polling"],
                "advice": {"timeout": 20000, "interval": 0},
            }
        ]

        try:
            async with self.session.post(
                handshake_url, json=handshake_message, headers=headers
            ) as response:
                if response.status != 200:
                    logger.error(
                        f"Handshake failed with status {response.status}",
                        extra={'route_section': 'CometD_Handshake'},
                    )
                    return False

                result = await response.json()
                if result and len(result) > 0:
                    handshake_response = result[0]
                    if handshake_response.get("successful"):
                        self.client_id = handshake_response.get("clientId")
                        logger.info(
                            f"Handshake successful. Client ID: {self.client_id}",
                            extra={
                                'route_section': 'CometD_Handshake',
                                'additional_details': f"Client ID: {self.client_id}",
                            },
                        )
                        return True
                    else:
                        logger.error(
                            f"Handshake failed: {handshake_response}",
                            extra={'route_section': 'CometD_Handshake'},
                        )
                        return False

        except Exception as e:
            logger.error(
                f"Error during handshake: {str(e)}",
                extra={'route_section': 'CometD_Handshake'},
            )
            return False

        return False

    async def _connect(self) -> bool:
        """
        Establish CometD connection after handshake
        """
        connect_url = f"{self.sf_instance_url}/cometd/{self.cometd_version}/"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        connect_message = [
            {
                "channel": "/meta/connect",
                "clientId": self.client_id,
                "connectionType": "long-polling",
            }
        ]

        try:
            async with self.session.post(
                connect_url, json=connect_message, headers=headers
            ) as response:
                if response.status != 200:
                    logger.error(
                        f"Connect failed with status {response.status}",
                        extra={'route_section': 'CometD_Connect'},
                    )
                    return False

                result = await response.json()
                if result and len(result) > 0:
                    connect_response = result[0]
                    if connect_response.get("successful"):
                        logger.info(
                            "CometD connect successful",
                            extra={'route_section': 'CometD_Connect'},
                        )
                        return True
                    else:
                        logger.error(
                            f"Connect failed: {connect_response}",
                            extra={'route_section': 'CometD_Connect'},
                        )
                        return False

        except Exception as e:
            logger.error(
                f"Error during connect: {str(e)}",
                extra={'route_section': 'CometD_Connect'},
            )
            return False

        return False

    async def subscribe(self, channel: str) -> bool:
        """
        Subscribe to a specific channel
        """
        subscribe_url = f"{self.sf_instance_url}/cometd/{self.cometd_version}/"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        subscribe_message = [
            {
                "channel": "/meta/subscribe",
                "clientId": self.client_id,
                "subscription": channel,
            }
        ]

        try:
            async with self.session.post(
                subscribe_url, json=subscribe_message, headers=headers
            ) as response:
                if response.status != 200:
                    logger.error(
                        f"Subscribe failed with status {response.status}",
                        extra={'route_section': 'CometD_Subscribe'},
                    )
                    return False

                result = await response.json()
                if result and len(result) > 0:
                    subscribe_response = result[0]
                    if subscribe_response.get("successful"):
                        # Track subscribed channels for reconnection
                        if channel not in self.subscribed_channels:
                            self.subscribed_channels.append(channel)

                        logger.info(
                            f"Successfully subscribed to {channel}",
                            extra={
                                'route_section': 'CometD_Subscribe',
                                'additional_details': f"Channel: {channel}",
                            },
                        )
                        return True
                    else:
                        logger.error(
                            f"Subscribe failed: {subscribe_response}",
                            extra={'route_section': 'CometD_Subscribe'},
                        )
                        return False

        except Exception as e:
            logger.error(
                f"Error during subscribe: {str(e)}",
                extra={'route_section': 'CometD_Subscribe'},
            )
            return False

        return False

    async def listen(
        self,
        event_handler: typing.Callable[
            [typing.Dict[str, typing.Any]], typing.Awaitable[None]
        ],
    ) -> None:
        """
        Listen for events and handle them with the provided event handler
        """
        listen_url = f"{self.sf_instance_url}/cometd/{self.cometd_version}/"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        while self.is_connected:
            try:
                connect_message = [
                    {
                        "channel": "/meta/connect",
                        "clientId": self.client_id,
                        "connectionType": "long-polling",
                    }
                ]

                async with self.session.post(
                    listen_url,
                    json=connect_message,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(
                        total=self.connection_timeout
                    ),
                ) as response:
                    if response.status != 200:
                        logger.error(
                            f"Listen failed with status {response.status}",
                            extra={'route_section': 'CometD_Listen'},
                        )
                        await self._handle_connection_error()
                        continue

                    result = await response.json()
                    if not result:
                        continue

                    for message in result:
                        if message.get("channel") and message.get("data"):
                            # This is an event message
                            try:
                                await event_handler(message)
                            except Exception as e:
                                logger.error(
                                    f"Error handling event: {str(e)}",
                                    extra={'route_section': 'Event_Handler'},
                                )
                        elif not message.get("successful"):
                            # Check if server is advising us to reconnect
                            advice = message.get("advice", {})
                            if advice.get("reconnect") == "handshake":
                                logger.info(
                                    "Server advised to re-handshake. Reconnecting...",
                                    extra={'route_section': 'CometD_Listen'},
                                )
                                await self._handle_connection_error()
                                break
                            else:
                                logger.warning(
                                    f"Received unsuccessful message: {message}",
                                    extra={'route_section': 'CometD_Listen'},
                                )

            except asyncio.TimeoutError:
                logger.debug("Listen timeout, reconnecting...")
                continue
            except Exception as e:
                logger.error(
                    f"Error during listen: {str(e)}",
                    extra={'route_section': 'CometD_Listen'},
                )
                await self._handle_connection_error()

    async def _handle_connection_error(self) -> None:
        """
        Handle connection errors with exponential backoff reconnection
        """
        self.is_connected = False
        self.reconnect_attempts += 1

        if self.reconnect_attempts > self.max_reconnect_attempts:
            logger.error(
                f"Max reconnection attempts ({self.max_reconnect_attempts}) reached. Stopping.",
                extra={'route_section': 'CometD_Reconnection'},
            )
            return

        wait_time = self.reconnect_delay * (2 ** (self.reconnect_attempts - 1))
        logger.info(
            f"Connection error. Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts} in {wait_time} seconds...",
            extra={'route_section': 'CometD_Reconnection'},
        )

        await asyncio.sleep(wait_time)

        # Reset client state to force full re-authentication
        self.client_id = None
        self.is_authenticated = False

        # Try to reconnect with full re-authentication
        if await self.authenticate() and await self.connect():
            # Re-subscribe to all previously subscribed channels
            channels_to_resubscribe = self.subscribed_channels.copy()
            self.subscribed_channels.clear()  # Clear list to avoid duplicates

            resubscribe_success = True
            for channel in channels_to_resubscribe:
                if not await self.subscribe(channel):
                    logger.error(
                        f"Failed to re-subscribe to channel: {channel}",
                        extra={'route_section': 'CometD_Reconnection'},
                    )
                    resubscribe_success = False

            if resubscribe_success:
                logger.info(
                    "Reconnection successful",
                    extra={'route_section': 'CometD_Reconnection'},
                )
                self.reconnect_attempts = (
                    0  # Reset counter on successful reconnection
                )
            else:
                logger.warning(
                    "Reconnection partially successful - some channel subscriptions failed",
                    extra={'route_section': 'CometD_Reconnection'},
                )
        else:
            logger.error(
                "Reconnection failed",
                extra={'route_section': 'CometD_Reconnection'},
            )

    async def disconnect(self) -> None:
        """
        Disconnect from CometD
        """
        if not self.client_id or not self.session:
            return

        disconnect_url = (
            f"{self.sf_instance_url}/cometd/{self.cometd_version}/"
        )
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        disconnect_message = [
            {
                "channel": "/meta/disconnect",
                "clientId": self.client_id,
            }
        ]

        try:
            async with self.session.post(
                disconnect_url, json=disconnect_message, headers=headers
            ) as _:
                logger.info(
                    "Disconnected from CometD",
                    extra={'route_section': 'CometD_Disconnect'},
                )
        except Exception as e:
            logger.error(
                f"Error during disconnect: {str(e)}",
                extra={'route_section': 'CometD_Disconnect'},
            )

        self.is_connected = False
        if self.session:
            await self.session.close()
            self.session = None
