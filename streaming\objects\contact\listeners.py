# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error,reportReturnType=error
"""
Contact Account Change Event listener.
Handles Contact Account Change Events from Salesforce.
Contact (Salesforce) = Legal Personnel (PostgreSQL)
"""
import typing

from ...shared.base.listener import BaseSalesforceEventListener
from ...shared.utils.config import EVENT_CHANNELS
from ...shared.utils.logger import get_streaming_logger
from .handlers import ContactChangeHandler

logger = get_streaming_logger()


class ContactChangeEventListener(BaseSalesforceEventListener):
    """
    Specific listener for Contact Account Change Events to handle contact re-assignment
    Contact (Salesforce) = Legal Personnel (PostgreSQL)
    """

    def __init__(self, test: bool = False):
        super().__init__(EVENT_CHANNELS['contact_account_change'], test)
        self.handler = ContactChangeHandler()

    async def handle_event(
        self, message: typing.Dict[str, typing.Any]
    ) -> None:
        """
        Handle incoming Contact Account Change Event (implements abstract method)
        """
        await self._handle_contact_account_change_event(message)

    async def _handle_contact_account_change_event(
        self, message: typing.Dict[str, typing.Any]
    ) -> None:
        """
        Handle incoming Contact Account Change Event
        """
        try:
            channel = message.get("channel")
            if channel != self.channel:
                return

            event_data = message.get("data", {})
            payload = event_data.get("payload", {})

            # Extract the required fields
            contact_gain_id = payload.get("Contact_Gain_Id__c")
            account_gain_id = payload.get("Account_Gain_Id__c")
            account_name = payload.get("Account_Name__c")

            if not contact_gain_id or not account_gain_id:
                logger.warning(
                    f"Missing required fields in Contact Account Change Event: {payload}",
                    extra={
                        'route_section': 'Contact_Account_Change_Event_Handler',
                        'additional_details': f"Event payload: {payload}",
                    },
                )
                return

            logger.info(
                f"Received Contact Account Change Event - Contact: {contact_gain_id}, Account: {account_gain_id}",
                extra={
                    'route_section': 'Contact_Account_Change_Event_Handler',
                    'additional_details': f"Account Name: {account_name}",
                },
            )

            # Process the contact reassignment
            await self.handler.process_contact_reassignment(
                contact_gain_id=contact_gain_id,
                new_account_gain_id=account_gain_id,
                account_name=account_name,
            )

        except Exception as e:
            logger.error(
                f"Error handling Contact Account Change Event: {str(e)}",
                extra={
                    'route_section': 'Contact_Account_Change_Event_Handler',
                    'additional_details': f"Message: {message}",
                },
            )
