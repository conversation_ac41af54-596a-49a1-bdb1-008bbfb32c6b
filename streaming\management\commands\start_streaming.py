# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error,reportReturnType=error
"""
Management command to manually start streaming listeners.
"""
import asyncio
import typing

from django.core.management.base import BaseCommand

from streaming.objects.contact.listeners import ContactChangeEventListener


class Command(BaseCommand):
    help = 'Start Salesforce streaming listeners manually'

    def add_arguments(self, parser: typing.Any) -> None:
        parser.add_argument(
            '--test',
            action='store_true',
            help='Use test Salesforce environment',
        )
        parser.add_argument(
            '--listener',
            type=str,
            default='contact',
            choices=['contact', 'all'],
            help='Which listener to start (default: contact)',
        )

    def handle(self, *args: typing.Any, **options: typing.Any) -> None:
        test_mode = options['test']
        listener_type = options['listener']

        self.stdout.write(
            self.style.SUCCESS(
                f'Starting streaming listeners (test={test_mode})...'
            )
        )

        async def start_listeners():
            if listener_type in ['contact', 'all']:
                self.stdout.write('Starting Contact Change Event listener...')
                contact_listener = ContactChangeEventListener(test=test_mode)
                await contact_listener.start_listening()

        # Run the async listeners
        try:
            asyncio.run(start_listeners())
        except KeyboardInterrupt:
            self.stdout.write(
                self.style.WARNING('Streaming listeners stopped by user')
            )
