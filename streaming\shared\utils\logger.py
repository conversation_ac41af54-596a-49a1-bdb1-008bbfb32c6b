# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error,reportReturnType=error
"""
Logging utilities for streaming functionality.
"""
import typing

from integration.integration_code import logger_config


def get_streaming_logger(name: str | None = None) -> typing.Any:
    """
    Get a logger configured for streaming operations.
    Uses the existing integration logging framework.
    """
    return logger_config.get_logger()


def log_event_received(
    channel: str, event_data: typing.Dict[str, typing.Any], logger: typing.Any
) -> None:
    """
    Log that an event was received on a channel.
    """
    logger.info(
        f"Event received on {channel}",
        extra={
            'route_section': 'Event_Reception',
            'additional_details': f"Channel: {channel}, Data keys: {list(event_data.keys())}",
        },
    )


def log_event_processing_start(
    event_type: str, event_id: str, logger: typing.Any
) -> None:
    """
    Log the start of event processing.
    """
    logger.info(
        f"Starting {event_type} processing for {event_id}",
        extra={
            'route_section': f'{event_type}_Processing',
            'additional_details': f"Event ID: {event_id}",
        },
    )


def log_event_processing_success(
    event_type: str, event_id: str, details: str, logger: typing.Any
) -> None:
    """
    Log successful event processing.
    """
    logger.info(
        f"Successfully processed {event_type} for {event_id}",
        extra={
            'route_section': f'{event_type}_Processing',
            'additional_details': details,
        },
    )


def log_event_processing_error(
    event_type: str, event_id: str, error: str, logger: typing.Any
) -> None:
    """
    Log event processing error.
    """
    logger.error(
        f"Error processing {event_type} for {event_id}: {error}",
        extra={
            'route_section': f'{event_type}_Processing',
            'additional_details': f"Event ID: {event_id}, Error: {error}",
        },
    )
