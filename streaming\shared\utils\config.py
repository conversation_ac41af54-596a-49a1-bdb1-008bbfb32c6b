# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error,reportReturnType=error
"""
Configuration for Salesforce Streaming functionality.
"""

# Configuration for Salesforce Streaming
STREAMING_CONFIG = {
    'cometd_version': '57.0',
    'connection_timeout': 120,  # seconds
    'max_reconnect_attempts': 5,
    'reconnect_delay': 5,  # seconds
    'batch_size': 500,  # for database operations
}

# Event channel configurations
# Note: Keys should be specific to the type of change (e.g., contact_account_change, contact_status_change)
# since there can be multiple change events per Salesforce object
EVENT_CHANNELS = {
    'contact_account_change': '/event/Contact_Account_Change__e',  # When contact's account changes
    'account_change': '/event/Account_Change_Event__e',  # Future use
    'case_change': '/event/Case_Change_Event__e',  # Future use
}

# Default settings for event listeners
DEFAULT_LISTENER_SETTINGS = {
    'auto_reconnect': True,
    'log_all_events': True,
    'validate_payloads': True,
}
