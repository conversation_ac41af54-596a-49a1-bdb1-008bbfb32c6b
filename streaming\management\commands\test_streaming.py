# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error,reportReturnType=error
"""
Management command to test streaming functionality.
"""
import asyncio
import typing

from django.core.management.base import BaseCommand

from streaming.objects.contact.listeners import ContactChangeEventListener


class Command(BaseCommand):
    help = 'Test Salesforce streaming connection'

    def add_arguments(self, parser: typing.Any) -> None:
        parser.add_argument(
            '--duration',
            type=int,
            default=30,
            help='How long to run the test (seconds)',
        )
        parser.add_argument(
            '--test',
            action='store_true',
            help='Use test Salesforce environment',
        )

    def handle(self, *args: typing.Any, **options: typing.Any) -> None:
        duration = options['duration']
        test_mode = options['test']

        self.stdout.write(
            self.style.SUCCESS(
                f'Testing streaming connection for {duration} seconds (test={test_mode})...'
            )
        )

        async def test_streaming():
            listener = ContactChangeEventListener(test=test_mode)

            try:
                self.stdout.write('Starting streaming connection...')

                # Start listening in the background
                listen_task = asyncio.create_task(listener.start_listening())

                # Wait for the specified duration
                await asyncio.sleep(duration)

                # Cancel the listening task
                listen_task.cancel()

                try:
                    await listen_task
                except asyncio.CancelledError:
                    pass

                self.stdout.write(
                    self.style.SUCCESS('Streaming test completed successfully')
                )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Streaming test failed: {str(e)}')
                )
            finally:
                if listener.streaming_client:
                    await listener.streaming_client.disconnect()
                self.stdout.write('Streaming connection closed')

        # Run the async test
        try:
            asyncio.run(test_streaming())
        except KeyboardInterrupt:
            self.stdout.write(
                self.style.WARNING('Streaming test stopped by user')
            )
