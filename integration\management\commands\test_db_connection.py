# pyright: reportMissingTypeArgument=error,reportMissingParameterType=error,reportReturnType=error
import typing

from django.core.management.base import BaseCommand

from integration.integration_code import aws_operations, logger_config

logger = logger_config.get_logger()


class Command(BaseCommand):
    help = 'Test PostgreSQL database connection and find sample contact data'

    def handle(self, *args: typing.Any, **options: typing.Any) -> None:
        self.stdout.write(
            self.style.SUCCESS(
                '=== Testing PostgreSQL Database Connection ==='
            )
        )

        try:
            import psycopg

            # Get connection string
            conn_string = aws_operations.get_postgres_conn_string()
            self.stdout.write(f'Connection string: {conn_string}')

            # Test connection
            with psycopg.connect(conn_string) as conn:
                self.stdout.write(
                    self.style.SUCCESS(
                        '✅ Successfully connected to PostgreSQL!'
                    )
                )

                with conn.cursor() as cur:
                    # Query for a few contacts with gainid
                    self.stdout.write('\n--- Sample Contact Data ---')
                    cur.execute(
                        """
                        SELECT gainid, lawfirmid, lawfirmname, modifieddatetime
                        FROM legalpersonnel
                        WHERE gainid IS NOT NULL
                        LIMIT 5
                    """
                    )
                    results = cur.fetchall()

                    if results:
                        self.stdout.write(
                            f'Found {len(results)} contacts with Gain IDs:'
                        )
                        for i, row in enumerate(results, 1):
                            self.stdout.write(
                                f'  {i}. Gain ID: {row[0]}, Lawfirm ID: {row[1]}, Lawfirm Name: {row[2] or "Not set"}, Modified: {row[3]}'
                            )
                    else:
                        self.stdout.write(
                            self.style.WARNING(
                                '⚠️  No contacts found with Gain IDs'
                            )
                        )

                    # Get total count
                    cur.execute("SELECT COUNT(*) FROM legalpersonnel")
                    total_result = cur.fetchone()
                    total_count = total_result[0] if total_result else 0
                    self.stdout.write(
                        f'\nTotal legalpersonnel records: {total_count}'
                    )

                    # Get count with gainid
                    cur.execute(
                        "SELECT COUNT(*) FROM legalpersonnel WHERE gainid IS NOT NULL"
                    )
                    gainid_result = cur.fetchone()
                    gainid_count = gainid_result[0] if gainid_result else 0
                    self.stdout.write(f'Records with Gain ID: {gainid_count}')

                    # Test a sample update (dry run)
                    if results:
                        sample_gainid = results[0][0]
                        sample_lawfirmid = results[0][1]
                        sample_lawfirmname = results[0][2]

                        self.stdout.write(
                            f'\n--- Testing Update Query (DRY RUN) ---'
                        )
                        self.stdout.write(
                            f'Would update contact {sample_gainid} to new lawfirm (currently: {sample_lawfirmid}, "{sample_lawfirmname or "No name"}")'
                        )

                        # Show what the update query would look like
                        update_query = """
                            UPDATE legalpersonnel
                            SET lawfirmid = %s,
                                lawfirmname = %s,
                                modifieddatetime = CURRENT_TIMESTAMP
                            WHERE gainid = %s
                        """
                        self.stdout.write(
                            f'Update query: {update_query.strip()}'
                        )
                        self.stdout.write(
                            f'Parameters: ("new_lawfirm_id", "New Lawfirm Name", "{sample_gainid}")'
                        )

                        # Provide a sample Gain ID for testing
                        self.stdout.write(f'\n--- For Testing ---')
                        self.stdout.write(
                            f'Sample Contact Gain ID: {sample_gainid}'
                        )
                        self.stdout.write(
                            f'Current Lawfirm ID: {sample_lawfirmid}'
                        )
                        self.stdout.write(
                            f'Current Lawfirm Name: {sample_lawfirmname or "Not set"}'
                        )
                        self.stdout.write(
                            f'You can use this Gain ID to test the Contact Change Event functionality'
                        )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Database connection failed: {str(e)}')
            )

            # Check if it's an IP whitelist issue
            if 'timeout' in str(e).lower() or 'connection' in str(e).lower():
                self.stdout.write(
                    self.style.WARNING(
                        '\n💡 This might be an IP whitelist issue. '
                        'You may need to add your IP address to the PostgreSQL security group.'
                    )
                )
