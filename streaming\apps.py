import asyncio

from django.apps import AppConfig
from django.conf import settings


class StreamingConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'streaming'
    _streaming_started = False  # Class variable to prevent duplicate starts

    def ready(self) -> None:
        """
        Initialize streaming listeners when Django starts
        """
        import os

        # Only start streaming in the main process (not during migrations, etc.)
        # and prevent duplicate starts from Django's auto-reloader
        streaming_enabled = getattr(
            settings, 'SALESFORCE_STREAMING_ENABLED', False
        )
        run_main = os.environ.get('RUN_MAIN') == 'true'

        if (
            streaming_enabled
            and not StreamingConfig._streaming_started
            and run_main  # Only run in main process, not reloader
        ):
            StreamingConfig._streaming_started = True
            self._start_streaming_listeners()

    def _start_streaming_listeners(self) -> None:
        """
        Start the streaming listeners in the background
        """
        # Import logger outside try block to avoid UnboundLocalError
        try:
            from .shared.utils.logger import get_streaming_logger
        except ImportError:
            # Fallback to integration logger if streaming logger not available
            from integration.integration_code.logger_config import (
                get_logger as get_streaming_logger,
            )

        logger = get_streaming_logger()

        try:
            from .objects.contact.listeners import ContactChangeEventListener

            # Check if we should use test mode
            use_test = getattr(settings, 'SALESFORCE_STREAMING_USE_TEST', True)

            logger.info(
                "Starting Contact Change Event listener with Django app...",
                extra={
                    'route_section': 'Django_App_Startup',
                    'additional_details': f"Test mode: {use_test}",
                },
            )

            # Create and start the listener in the background
            listener = ContactChangeEventListener(test=use_test)

            # Start the listener in a background thread since Django doesn't have an event loop
            import threading

            def start_listener():
                try:
                    asyncio.run(listener.start_listening())
                except Exception as e:
                    logger.error(
                        f"Error in streaming listener thread: {str(e)}",
                        extra={
                            'route_section': 'Django_App_Startup',
                            'additional_details': f"Thread error: {str(e)}",
                        },
                    )

            # Start in daemon thread so it doesn't prevent Django shutdown
            thread = threading.Thread(target=start_listener, daemon=True)
            thread.start()

            logger.info(
                f"Contact Change Event listener started (test={use_test})",
                extra={
                    'route_section': 'Django_App_Startup',
                    'additional_details': f"Listener class: {listener.__class__.__name__}",
                },
            )

        except Exception as e:
            logger.error(
                f"Failed to start streaming listeners: {str(e)}",
                extra={
                    'route_section': 'Django_App_Startup',
                    'additional_details': f"Error: {str(e)}",
                },
            )
